"""
Unit tests for diagram models.
"""

import pytest

from mermaid_render.models import FlowchartDiagram, SequenceDiagram, ClassDiagram
from mermaid_render.models.flowchart import FlowchartNode, FlowchartEdge
from mermaid_render.models.sequence import SequenceParticipant, SequenceMessage
from mermaid_render.exceptions import DiagramError


class TestFlowchartDiagram:
    """Test FlowchartDiagram class."""
    
    def test_init_default(self):
        """Test flowchart initialization with defaults."""
        flowchart = FlowchartDiagram()
        
        assert flowchart.direction == "TD"
        assert flowchart.get_diagram_type() == "flowchart"
        assert len(flowchart.nodes) == 0
        assert len(flowchart.edges) == 0
    
    def test_init_custom_direction(self):
        """Test flowchart initialization with custom direction."""
        flowchart = FlowchartDiagram(direction="LR", title="Test Flowchart")
        
        assert flowchart.direction == "LR"
        assert flowchart.title == "Test Flowchart"
    
    def test_invalid_direction(self):
        """Test flowchart with invalid direction."""
        with pytest.raises(DiagramError, match="Invalid direction"):
            FlowchartDiagram(direction="INVALID")
    
    def test_add_node(self):
        """Test adding nodes to flowchart."""
        flowchart = FlowchartDiagram()
        
        node = flowchart.add_node("A", "Start", shape="circle")
        
        assert node.id == "A"
        assert node.label == "Start"
        assert node.shape == "circle"
        assert "A" in flowchart.nodes
        assert flowchart.nodes["A"] is node
    
    def test_add_duplicate_node(self):
        """Test adding duplicate node ID."""
        flowchart = FlowchartDiagram()
        flowchart.add_node("A", "First")
        
        with pytest.raises(DiagramError, match="already exists"):
            flowchart.add_node("A", "Second")
    
    def test_add_edge(self):
        """Test adding edges between nodes."""
        flowchart = FlowchartDiagram()
        flowchart.add_node("A", "Start")
        flowchart.add_node("B", "End")
        
        edge = flowchart.add_edge("A", "B", label="Connect")
        
        assert edge.from_node == "A"
        assert edge.to_node == "B"
        assert edge.label == "Connect"
        assert edge in flowchart.edges
    
    def test_add_edge_nonexistent_nodes(self):
        """Test adding edge with nonexistent nodes."""
        flowchart = FlowchartDiagram()
        
        with pytest.raises(DiagramError, match="does not exist"):
            flowchart.add_edge("A", "B")
    
    def test_add_subgraph(self):
        """Test adding subgraph."""
        flowchart = FlowchartDiagram()
        
        subgraph = flowchart.add_subgraph("sub1", title="Subprocess")
        
        assert subgraph.id == "sub1"
        assert subgraph.title == "Subprocess"
        assert "sub1" in flowchart.subgraphs
    
    def test_add_node_to_subgraph(self):
        """Test adding node to subgraph."""
        flowchart = FlowchartDiagram()
        flowchart.add_node("A", "Node")
        subgraph = flowchart.add_subgraph("sub1")
        
        flowchart.add_node_to_subgraph("A", "sub1")
        
        assert "A" in subgraph.nodes
    
    def test_to_mermaid(self):
        """Test generating Mermaid syntax."""
        flowchart = FlowchartDiagram(direction="LR")
        flowchart.add_node("A", "Start", shape="circle")
        flowchart.add_node("B", "End", shape="circle")
        flowchart.add_edge("A", "B", label="Flow")
        
        mermaid_code = flowchart.to_mermaid()
        
        assert "flowchart LR" in mermaid_code
        assert "A((Start))" in mermaid_code
        assert "B((End))" in mermaid_code
        assert "A -->|Flow| B" in mermaid_code


class TestFlowchartNode:
    """Test FlowchartNode class."""
    
    def test_init_default(self):
        """Test node initialization with defaults."""
        node = FlowchartNode("A", "Label")
        
        assert node.id == "A"
        assert node.label == "Label"
        assert node.shape == "rectangle"
    
    def test_init_custom_shape(self):
        """Test node initialization with custom shape."""
        node = FlowchartNode("B", "Circle Node", shape="circle")
        
        assert node.shape == "circle"
    
    def test_invalid_shape(self):
        """Test node with invalid shape."""
        with pytest.raises(DiagramError, match="Unknown node shape"):
            FlowchartNode("A", "Label", shape="invalid_shape")
    
    def test_to_mermaid_rectangle(self):
        """Test Mermaid syntax for rectangle node."""
        node = FlowchartNode("A", "Rectangle", shape="rectangle")
        
        assert node.to_mermaid() == "A[Rectangle]"
    
    def test_to_mermaid_circle(self):
        """Test Mermaid syntax for circle node."""
        node = FlowchartNode("B", "Circle", shape="circle")
        
        assert node.to_mermaid() == "B((Circle))"
    
    def test_to_mermaid_rhombus(self):
        """Test Mermaid syntax for rhombus node."""
        node = FlowchartNode("C", "Decision", shape="rhombus")
        
        assert node.to_mermaid() == "C{Decision}"


class TestFlowchartEdge:
    """Test FlowchartEdge class."""
    
    def test_init_default(self):
        """Test edge initialization with defaults."""
        edge = FlowchartEdge("A", "B")
        
        assert edge.from_node == "A"
        assert edge.to_node == "B"
        assert edge.arrow_type == "arrow"
        assert edge.label is None
    
    def test_init_with_label(self):
        """Test edge initialization with label."""
        edge = FlowchartEdge("A", "B", label="Connect", arrow_type="dotted")
        
        assert edge.label == "Connect"
        assert edge.arrow_type == "dotted"
    
    def test_invalid_arrow_type(self):
        """Test edge with invalid arrow type."""
        with pytest.raises(DiagramError, match="Unknown arrow type"):
            FlowchartEdge("A", "B", arrow_type="invalid")
    
    def test_to_mermaid_simple(self):
        """Test Mermaid syntax for simple edge."""
        edge = FlowchartEdge("A", "B")
        
        assert edge.to_mermaid() == "A --> B"
    
    def test_to_mermaid_with_label(self):
        """Test Mermaid syntax for edge with label."""
        edge = FlowchartEdge("A", "B", label="Yes", arrow_type="arrow")
        
        assert edge.to_mermaid() == "A -->|Yes| B"


class TestSequenceDiagram:
    """Test SequenceDiagram class."""
    
    def test_init_default(self):
        """Test sequence diagram initialization."""
        sequence = SequenceDiagram()
        
        assert sequence.get_diagram_type() == "sequenceDiagram"
        assert len(sequence.participants) == 0
        assert len(sequence.messages) == 0
        assert sequence.autonumber is False
    
    def test_init_with_options(self):
        """Test sequence diagram with options."""
        sequence = SequenceDiagram(title="Test Sequence", autonumber=True)
        
        assert sequence.title == "Test Sequence"
        assert sequence.autonumber is True
    
    def test_add_participant(self):
        """Test adding participant."""
        sequence = SequenceDiagram()
        
        participant = sequence.add_participant("A", "Alice")
        
        assert participant.id == "A"
        assert participant.name == "Alice"
        assert "A" in sequence.participants
    
    def test_add_message(self):
        """Test adding message between participants."""
        sequence = SequenceDiagram()
        
        message = sequence.add_message("A", "B", "Hello", "sync")
        
        assert message.from_participant == "A"
        assert message.to_participant == "B"
        assert message.message == "Hello"
        assert message.message_type == "sync"
        
        # Should auto-create participants
        assert "A" in sequence.participants
        assert "B" in sequence.participants
    
    def test_add_note(self):
        """Test adding note."""
        sequence = SequenceDiagram()
        sequence.add_participant("A", "Alice")
        
        note = sequence.add_note("Important note", "A", "right of")
        
        assert note.text == "Important note"
        assert note.participant == "A"
        assert note.position == "right of"
    
    def test_to_mermaid(self):
        """Test generating Mermaid syntax."""
        sequence = SequenceDiagram(title="Test", autonumber=True)
        sequence.add_participant("A", "Alice")
        sequence.add_participant("B", "Bob")
        sequence.add_message("A", "B", "Hello", "sync")
        
        mermaid_code = sequence.to_mermaid()
        
        assert "sequenceDiagram" in mermaid_code
        assert "title: Test" in mermaid_code
        assert "autonumber" in mermaid_code
        assert "participant A as Alice" in mermaid_code
        assert "A->B: Hello" in mermaid_code


class TestSequenceParticipant:
    """Test SequenceParticipant class."""
    
    def test_init_default(self):
        """Test participant initialization."""
        participant = SequenceParticipant("A")
        
        assert participant.id == "A"
        assert participant.name == "A"  # Defaults to ID
    
    def test_init_with_name(self):
        """Test participant with custom name."""
        participant = SequenceParticipant("A", "Alice")
        
        assert participant.id == "A"
        assert participant.name == "Alice"
    
    def test_to_mermaid_simple(self):
        """Test Mermaid syntax for simple participant."""
        participant = SequenceParticipant("A")
        
        assert participant.to_mermaid() == "participant A"
    
    def test_to_mermaid_with_name(self):
        """Test Mermaid syntax for participant with name."""
        participant = SequenceParticipant("A", "Alice")
        
        assert participant.to_mermaid() == "participant A as Alice"


class TestSequenceMessage:
    """Test SequenceMessage class."""
    
    def test_init_default(self):
        """Test message initialization."""
        message = SequenceMessage("A", "B", "Hello")
        
        assert message.from_participant == "A"
        assert message.to_participant == "B"
        assert message.message == "Hello"
        assert message.message_type == "sync"
    
    def test_invalid_message_type(self):
        """Test message with invalid type."""
        with pytest.raises(DiagramError, match="Unknown message type"):
            SequenceMessage("A", "B", "Hello", "invalid_type")
    
    def test_to_mermaid_simple(self):
        """Test Mermaid syntax for simple message."""
        message = SequenceMessage("A", "B", "Hello", "sync")
        
        lines = message.to_mermaid()
        assert "A->B: Hello" in lines
    
    def test_to_mermaid_with_activation(self):
        """Test Mermaid syntax with activation."""
        message = SequenceMessage("A", "B", "Hello", "sync", activate=True)
        
        lines = message.to_mermaid()
        assert "activate B" in lines
        assert "A->B: Hello" in lines
