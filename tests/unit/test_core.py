"""
Unit tests for core functionality.
"""

import pytest
from unittest.mock import Mock, patch

from mermaid_render.core import Mermaid<PERSON>onfig, MermaidTheme, MermaidRenderer
from mermaid_render.exceptions import (
    ConfigurationError,
    UnsupportedFormatError,
    RenderingError,
)


class TestMermaidConfig:
    """Test MermaidConfig class."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = MermaidConfig()
        
        assert config.get("server_url") == "https://mermaid.ink"
        assert config.get("timeout") == 30
        assert config.get("default_theme") == "default"
        assert config.get("validate_syntax") is True
    
    def test_custom_config(self):
        """Test custom configuration values."""
        custom_config = {
            "timeout": 60,
            "default_theme": "dark",
            "custom_option": "test_value"
        }
        config = MermaidConfig(**custom_config)
        
        assert config.get("timeout") == 60
        assert config.get("default_theme") == "dark"
        assert config.get("custom_option") == "test_value"
        assert config.get("server_url") == "https://mermaid.ink"  # Default preserved
    
    def test_get_with_default(self):
        """Test getting config value with default."""
        config = MermaidConfig()
        
        assert config.get("nonexistent", "default_value") == "default_value"
        assert config.get("timeout", 999) == 30  # Actual value returned
    
    def test_set_config(self):
        """Test setting configuration values."""
        config = MermaidConfig()
        
        config.set("new_option", "new_value")
        assert config.get("new_option") == "new_value"
        
        config.set("timeout", 45)
        assert config.get("timeout") == 45
    
    def test_update_config(self):
        """Test updating multiple configuration values."""
        config = MermaidConfig()
        
        updates = {
            "timeout": 120,
            "retries": 5,
            "new_setting": "test"
        }
        config.update(updates)
        
        assert config.get("timeout") == 120
        assert config.get("retries") == 5
        assert config.get("new_setting") == "test"
    
    def test_to_dict(self):
        """Test converting config to dictionary."""
        config = MermaidConfig(timeout=60, custom="value")
        config_dict = config.to_dict()
        
        assert isinstance(config_dict, dict)
        assert config_dict["timeout"] == 60
        assert config_dict["custom"] == "value"
        
        # Ensure it's a copy
        config_dict["timeout"] = 999
        assert config.get("timeout") == 60


class TestMermaidTheme:
    """Test MermaidTheme class."""
    
    def test_built_in_theme(self):
        """Test creating built-in theme."""
        theme = MermaidTheme("dark")
        
        assert theme.name == "dark"
        config = theme.to_dict()
        assert config["theme"] == "dark"
    
    def test_custom_theme(self):
        """Test creating custom theme."""
        custom_config = {
            "primaryColor": "#ff0000",
            "lineColor": "#000000"
        }
        theme = MermaidTheme("custom", **custom_config)
        
        assert theme.name == "custom"
        config = theme.to_dict()
        assert config["primaryColor"] == "#ff0000"
        assert config["lineColor"] == "#000000"
    
    def test_unknown_theme(self):
        """Test creating theme with unknown name."""
        with pytest.raises(ConfigurationError, match="Unknown theme"):
            MermaidTheme("nonexistent_theme")
    
    def test_theme_override(self):
        """Test overriding built-in theme settings."""
        theme = MermaidTheme("default", primaryColor="#custom")
        
        config = theme.to_dict()
        assert config["primaryColor"] == "#custom"
    
    def test_apply_to_config(self):
        """Test applying theme to configuration."""
        theme = MermaidTheme("dark")
        base_config = {"width": 800, "height": 600}
        
        result = theme.apply_to_config(base_config)
        
        assert result["width"] == 800  # Original preserved
        assert result["height"] == 600  # Original preserved
        assert result["theme"] == "dark"  # Theme applied


class TestMermaidRenderer:
    """Test MermaidRenderer class."""
    
    def test_init_default(self):
        """Test renderer initialization with defaults."""
        renderer = MermaidRenderer()
        
        assert renderer.config is not None
        assert renderer.get_theme() is None
        assert "svg" in renderer.SUPPORTED_FORMATS
    
    def test_init_with_config(self, mermaid_config):
        """Test renderer initialization with config."""
        renderer = MermaidRenderer(config=mermaid_config)
        
        assert renderer.config is mermaid_config
    
    def test_init_with_theme_string(self):
        """Test renderer initialization with theme string."""
        renderer = MermaidRenderer(theme="dark")
        
        theme = renderer.get_theme()
        assert theme is not None
        assert theme.name == "dark"
    
    def test_init_with_theme_object(self):
        """Test renderer initialization with theme object."""
        theme = MermaidTheme("forest")
        renderer = MermaidRenderer(theme=theme)
        
        assert renderer.get_theme() is theme
    
    def test_set_theme_string(self):
        """Test setting theme with string."""
        renderer = MermaidRenderer()
        renderer.set_theme("neutral")
        
        theme = renderer.get_theme()
        assert theme.name == "neutral"
    
    def test_set_theme_object(self):
        """Test setting theme with object."""
        renderer = MermaidRenderer()
        theme = MermaidTheme("base")
        renderer.set_theme(theme)
        
        assert renderer.get_theme() is theme
    
    def test_set_invalid_theme(self):
        """Test setting invalid theme."""
        renderer = MermaidRenderer()
        
        with pytest.raises(ConfigurationError, match="Invalid theme type"):
            renderer.set_theme(123)
    
    def test_unsupported_format(self, mermaid_renderer, sample_mermaid_code):
        """Test rendering with unsupported format."""
        with pytest.raises(UnsupportedFormatError, match="Unsupported format"):
            mermaid_renderer.render(sample_mermaid_code, format="gif")
    
    @patch('mermaid_render.core.md.Mermaid')
    def test_render_svg_success(self, mock_mermaid, mermaid_renderer, sample_mermaid_code):
        """Test successful SVG rendering."""
        mock_instance = Mock()
        mock_instance.__str__ = Mock(return_value="<svg>test</svg>")
        mock_mermaid.return_value = mock_instance
        
        result = mermaid_renderer.render(sample_mermaid_code, format="svg")
        
        assert result == "<svg>test</svg>"
        mock_mermaid.assert_called_once_with(sample_mermaid_code)
    
    @patch('mermaid_render.core.md.Mermaid')
    def test_render_with_validation_disabled(self, mock_mermaid, invalid_mermaid_code):
        """Test rendering with validation disabled."""
        config = MermaidConfig(validate_syntax=False)
        renderer = MermaidRenderer(config=config)
        
        mock_instance = Mock()
        mock_instance.__str__ = Mock(return_value="<svg>test</svg>")
        mock_mermaid.return_value = mock_instance
        
        # Should not raise validation error
        result = renderer.render(invalid_mermaid_code, format="svg")
        assert result == "<svg>test</svg>"
    
    def test_render_with_validation_enabled(self, invalid_mermaid_code):
        """Test rendering with validation enabled."""
        config = MermaidConfig(validate_syntax=True)
        renderer = MermaidRenderer(config=config)
        
        # Should raise validation error
        with pytest.raises(Exception):  # ValidationError or similar
            renderer.render(invalid_mermaid_code, format="svg")
    
    @patch('mermaid_render.core.md.Mermaid')
    def test_render_diagram_object(self, mock_mermaid, mermaid_renderer, sample_flowchart):
        """Test rendering diagram object."""
        mock_instance = Mock()
        mock_instance.__str__ = Mock(return_value="<svg>flowchart</svg>")
        mock_mermaid.return_value = mock_instance
        
        result = mermaid_renderer.render(sample_flowchart, format="svg")
        
        assert result == "<svg>flowchart</svg>"
        # Should call to_mermaid() on the diagram
        expected_code = sample_flowchart.to_mermaid()
        mock_mermaid.assert_called_once_with(expected_code)
    
    @patch('mermaid_render.core.md.Mermaid')
    def test_save_to_file(self, mock_mermaid, mermaid_renderer, sample_mermaid_code, temp_dir):
        """Test saving diagram to file."""
        mock_instance = Mock()
        mock_instance.__str__ = Mock(return_value="<svg>test content</svg>")
        mock_mermaid.return_value = mock_instance
        
        output_path = temp_dir / "test.svg"
        mermaid_renderer.save(sample_mermaid_code, output_path)
        
        assert output_path.exists()
        content = output_path.read_text()
        assert content == "<svg>test content</svg>"
    
    def test_save_format_inference(self, mermaid_renderer, sample_mermaid_code, temp_dir):
        """Test format inference from file extension."""
        with patch('mermaid_render.core.md.Mermaid') as mock_mermaid:
            mock_instance = Mock()
            mock_instance.__str__ = Mock(return_value="<svg>test</svg>")
            mock_mermaid.return_value = mock_instance
            
            # Test SVG extension
            svg_path = temp_dir / "test.svg"
            mermaid_renderer.save(sample_mermaid_code, svg_path)
            assert svg_path.exists()
    
    def test_save_directory_creation(self, mermaid_renderer, sample_mermaid_code, temp_dir):
        """Test automatic directory creation when saving."""
        with patch('mermaid_render.core.md.Mermaid') as mock_mermaid:
            mock_instance = Mock()
            mock_instance.__str__ = Mock(return_value="<svg>test</svg>")
            mock_mermaid.return_value = mock_instance
            
            # Create nested path that doesn't exist
            nested_path = temp_dir / "subdir" / "nested" / "test.svg"
            mermaid_renderer.save(sample_mermaid_code, nested_path)
            
            assert nested_path.exists()
            assert nested_path.parent.exists()
