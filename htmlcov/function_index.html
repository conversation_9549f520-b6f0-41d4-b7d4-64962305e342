<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">23%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 10:26 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276___init___py.html#t256">mermaid_render/__init__.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276___init___py.html#t256"><data value='quick_render'>quick_render</data></a></td>
                <td>19</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="16 19">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276___init___py.html">mermaid_render/__init__.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="33 42">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c___init___py.html">mermaid_render/ai/__init__.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="1 8">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t20">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t20"><data value='to_dict'>ComplexityAnalysis.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t41">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t41"><data value='to_dict'>QualityMetrics.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t63">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t63"><data value='to_dict'>AnalysisReport.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t78">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t78"><data value='init__'>DiagramAnalyzer.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t81">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t81"><data value='analyze'>DiagramAnalyzer.analyze</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t112">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t112"><data value='analyze_complexity'>DiagramAnalyzer.analyze_complexity</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t144">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t144"><data value='assess_quality'>DiagramAnalyzer.assess_quality</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t167">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t167"><data value='count_nodes'>DiagramAnalyzer._count_nodes</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t184">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t184"><data value='count_connections'>DiagramAnalyzer._count_connections</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t199">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t199"><data value='calculate_depth'>DiagramAnalyzer._calculate_depth</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t213">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t213"><data value='calculate_branching_factor'>DiagramAnalyzer._calculate_branching_factor</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t221">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t221"><data value='calculate_complexity_score'>DiagramAnalyzer._calculate_complexity_score</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t245">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t245"><data value='assess_readability'>DiagramAnalyzer._assess_readability</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t268">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t268"><data value='assess_consistency'>DiagramAnalyzer._assess_consistency</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t286">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t286"><data value='assess_completeness'>DiagramAnalyzer._assess_completeness</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t304">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t304"><data value='assess_accessibility'>DiagramAnalyzer._assess_accessibility</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t322">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t322"><data value='identify_issues'>DiagramAnalyzer._identify_issues</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t348">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t348"><data value='generate_recommendations'>DiagramAnalyzer._generate_recommendations</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t375">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t375"><data value='identify_strengths'>DiagramAnalyzer._identify_strengths</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t402">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t402"><data value='has_proper_spacing'>DiagramAnalyzer._has_proper_spacing</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t406">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t406"><data value='has_clear_labels'>DiagramAnalyzer._has_clear_labels</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t412">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t412"><data value='has_direction_specified'>DiagramAnalyzer._has_direction_specified</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t416">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t416"><data value='has_consistent_naming'>DiagramAnalyzer._has_consistent_naming</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t426">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t426"><data value='has_consistent_styling'>DiagramAnalyzer._has_consistent_styling</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t430">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t430"><data value='has_consistent_connections'>DiagramAnalyzer._has_consistent_connections</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t435">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t435"><data value='has_start_end_nodes'>DiagramAnalyzer._has_start_end_nodes</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t439">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t439"><data value='has_proper_connections'>DiagramAnalyzer._has_proper_connections</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t445">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t445"><data value='has_meaningful_content'>DiagramAnalyzer._has_meaningful_content</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t449">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t449"><data value='relies_on_color_only'>DiagramAnalyzer._relies_on_color_only</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t456">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t456"><data value='has_descriptive_labels'>DiagramAnalyzer._has_descriptive_labels</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t461">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t461"><data value='has_poor_contrast'>DiagramAnalyzer._has_poor_contrast</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t466">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html#t466"><data value='load_quality_rules'>DiagramAnalyzer._load_quality_rules</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html">mermaid_render/ai/analysis.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_analysis_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t46">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t46"><data value='to_dict'>GenerationConfig.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t71">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t71"><data value='to_dict'>GenerationResult.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t91">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t91"><data value='init__'>DiagramGenerator.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t110">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t110"><data value='from_text'>DiagramGenerator.from_text</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t158">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t158"><data value='from_data'>DiagramGenerator.from_data</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t184">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t184"><data value='from_code'>DiagramGenerator.from_code</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t213">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t213"><data value='improve_diagram'>DiagramGenerator.improve_diagram</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t259">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t259"><data value='determine_diagram_type'>DiagramGenerator._determine_diagram_type</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t293">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t293"><data value='generate_with_ai'>DiagramGenerator._generate_with_ai</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t311">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t311"><data value='create_generation_prompt'>DiagramGenerator._create_generation_prompt</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t342">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t342"><data value='post_process_diagram'>DiagramGenerator._post_process_diagram</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t362">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t362"><data value='clean_diagram_code'>DiagramGenerator._clean_diagram_code</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t373">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t373"><data value='validate_diagram_syntax'>DiagramGenerator._validate_diagram_syntax</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t388">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t388"><data value='fix_common_issues'>DiagramGenerator._fix_common_issues</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t399">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t399"><data value='add_styling'>DiagramGenerator._add_styling</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t421">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t421"><data value='add_comments'>DiagramGenerator._add_comments</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t431">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t431"><data value='calculate_confidence'>DiagramGenerator._calculate_confidence</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t461">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t461"><data value='generate_suggestions'>DiagramGenerator._generate_suggestions</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t487">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t487"><data value='count_nodes'>DiagramGenerator._count_nodes</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t502">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t502"><data value='assess_diagram_complexity'>DiagramGenerator._assess_diagram_complexity</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t514">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t514"><data value='extract_diagram_code'>DiagramGenerator._extract_diagram_code</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t526">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t526"><data value='detect_diagram_type'>DiagramGenerator._detect_diagram_type</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t549">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t549"><data value='load_generation_templates'>DiagramGenerator._load_generation_templates</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t558">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t558"><data value='load_generation_prompts'>DiagramGenerator._load_generation_prompts</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t590">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t590"><data value='data_to_description'>DiagramGenerator._data_to_description</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t595">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t595"><data value='analyze_code_structure'>DiagramGenerator._analyze_code_structure</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t605">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t605"><data value='code_to_description'>DiagramGenerator._code_to_description</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t610">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t610"><data value='create_improvement_prompt'>DiagramGenerator._create_improvement_prompt</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t628">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html#t628"><data value='calculate_improvement_confidence'>DiagramGenerator._calculate_improvement_confidence</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html">mermaid_render/ai/diagram_generator.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_diagram_generator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>67</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="9 67">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t22">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t22"><data value='to_dict'>EntityExtraction.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t38">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t38"><data value='to_dict'>IntentClassification.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t59">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t59"><data value='to_dict'>TextAnalysis.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t80">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t80"><data value='init__'>NLProcessor.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t87">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t87"><data value='analyze_text'>NLProcessor.analyze_text</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t121">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t121"><data value='extract_keywords'>NLProcessor.extract_keywords</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t154">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t154"><data value='extract_entities'>NLProcessor.extract_entities</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t189">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t189"><data value='classify_intent'>NLProcessor.classify_intent</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t234">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t234"><data value='calculate_complexity'>NLProcessor.calculate_complexity</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t288">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t288"><data value='determine_domain'>NLProcessor.determine_domain</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t322">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t322"><data value='score_keywords'>NLProcessor._score_keywords</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t351">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t351"><data value='extract_relationships'>NLProcessor._extract_relationships</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t378">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t378"><data value='load_domain_keywords'>NLProcessor._load_domain_keywords</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t405">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t405"><data value='load_intent_patterns'>NLProcessor._load_intent_patterns</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t430">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t430"><data value='load_entity_patterns'>NLProcessor._load_entity_patterns</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t451">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html#t451"><data value='load_stopwords'>NLProcessor._load_stopwords</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html">mermaid_render/ai/nl_processor.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_nl_processor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t26">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t26"><data value='to_dict'>OptimizationResult.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t39">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t39"><data value='optimize'>LayoutOptimizer.optimize</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t62">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t62"><data value='has_proper_spacing'>LayoutOptimizer._has_proper_spacing</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t67">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t67"><data value='add_spacing'>LayoutOptimizer._add_spacing</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t80">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t80"><data value='needs_layout_optimization'>LayoutOptimizer._needs_layout_optimization</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t84">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t84"><data value='optimize_node_layout'>LayoutOptimizer._optimize_node_layout</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t95">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t95"><data value='optimize'>StyleOptimizer.optimize</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t118">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t118"><data value='add_basic_styling'>StyleOptimizer._add_basic_styling</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t126">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t126"><data value='needs_color_optimization'>StyleOptimizer._needs_color_optimization</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t130">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t130"><data value='optimize_colors'>StyleOptimizer._optimize_colors</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t141">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t141"><data value='init__'>DiagramOptimizer.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t145">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t145"><data value='optimize_layout'>DiagramOptimizer.optimize_layout</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t149">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t149"><data value='optimize_style'>DiagramOptimizer.optimize_style</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t153">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t153"><data value='optimize_all'>DiagramOptimizer.optimize_all</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t167">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html#t167"><data value='get_optimization_suggestions'>DiagramOptimizer.get_optimization_suggestions</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html">mermaid_render/ai/optimization.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_optimization_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t12">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t12"><data value='generate_text'>AIProvider.generate_text</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t17">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t17"><data value='is_available'>AIProvider.is_available</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t25">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t25"><data value='init__'>OpenAIProvider.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t30">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t30"><data value='generate_text'>OpenAIProvider.generate_text</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t52">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t52"><data value='is_available'>OpenAIProvider.is_available</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t60">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t60"><data value='fallback_generation'>OpenAIProvider._fallback_generation</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t78">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t78"><data value='init__'>AnthropicProvider.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t83">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t83"><data value='generate_text'>AnthropicProvider.generate_text</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t104">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t104"><data value='is_available'>AnthropicProvider.is_available</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t112">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t112"><data value='fallback_generation'>AnthropicProvider._fallback_generation</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t120">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t120"><data value='init__'>LocalModelProvider.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t124">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t124"><data value='generate_text'>LocalModelProvider.generate_text</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t129">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t129"><data value='is_available'>LocalModelProvider.is_available</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t133">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html#t133"><data value='template_generation'>LocalModelProvider._template_generation</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html">mermaid_render/ai/providers.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_providers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t42">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t42"><data value='post_init__'>Suggestion.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t46">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t46"><data value='to_dict'>Suggestion.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t64">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t64"><data value='init__'>SuggestionEngine.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t68">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t68"><data value='get_suggestions'>SuggestionEngine.get_suggestions</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t95">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t95"><data value='get_suggestions_by_type'>SuggestionEngine.get_suggestions_by_type</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t105">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t105"><data value='get_high_priority_suggestions'>SuggestionEngine.get_high_priority_suggestions</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t114">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t114"><data value='analyze_diagram'>SuggestionEngine._analyze_diagram</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t132">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t132"><data value='create_suggestion'>SuggestionEngine._create_suggestion</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t148">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t148"><data value='count_nodes'>SuggestionEngine._count_nodes</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t156">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t156"><data value='detect_diagram_type'>SuggestionEngine._detect_diagram_type</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t169">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t169"><data value='assess_complexity'>SuggestionEngine._assess_complexity</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t181">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html#t181"><data value='load_suggestion_rules'>SuggestionEngine._load_suggestion_rules</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html">mermaid_render/ai/suggestions.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_suggestions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t11">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t11"><data value='generate_from_text'>generate_from_text</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t43">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t43"><data value='optimize_diagram'>optimize_diagram</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t75">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t75"><data value='analyze_diagram'>analyze_diagram</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t92">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t92"><data value='get_suggestions'>get_suggestions</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t130">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t130"><data value='extract_entities'>extract_entities</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t146">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t146"><data value='classify_intent'>classify_intent</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t162">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t162"><data value='improve_diagram_with_ai'>improve_diagram_with_ai</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t186">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t186"><data value='get_diagram_insights'>get_diagram_insights</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t221">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t221"><data value='validate_ai_generated_diagram'>validate_ai_generated_diagram</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t263">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html#t263"><data value='generate_diagram_variations'>generate_diagram_variations</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html">mermaid_render/ai/utils.py</a></td>
                <td class="name left"><a href="z_2a0d4a6d23b7f11c_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a___init___py.html">mermaid_render/cache/__init__.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t49">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t49"><data value='post_init__'>CacheKey.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t53">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t53"><data value='to_string'>CacheKey.to_string</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t76">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t76"><data value='from_content'>CacheKey.from_content</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t118">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t118"><data value='post_init__'>CacheEntry.__post_init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t124">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t124"><data value='is_expired'>CacheEntry.is_expired</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t132">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t132"><data value='update_access'>CacheEntry.update_access</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t137">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t137"><data value='to_dict'>CacheEntry.to_dict</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t145">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t145"><data value='from_dict'>CacheEntry.from_dict</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t161">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t161"><data value='init__'>CacheManager.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t201">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t201"><data value='get'>CacheManager.get</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t250">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t250"><data value='put'>CacheManager.put</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t326">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t326"><data value='delete'>CacheManager.delete</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t351">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t351"><data value='clear'>CacheManager.clear</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t385">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t385"><data value='get_statistics'>CacheManager.get_statistics</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t403">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t403"><data value='get_performance_report'>CacheManager.get_performance_report</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t409">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html#t409"><data value='apply_strategy_before_put'>CacheManager._apply_strategy_before_put</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html">mermaid_render/cache/cache_manager.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_cache_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>58</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="58 58">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t26">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t26"><data value='optimize'>CacheOptimizer.optimize</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t47">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t47"><data value='init__'>CompressionOptimizer.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t72">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t72"><data value='optimize'>CompressionOptimizer.optimize</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t118">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t118"><data value='should_compress'>CompressionOptimizer._should_compress</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t137">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t137"><data value='compress_content'>CompressionOptimizer._compress_content</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t158">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t158"><data value='decompress_content'>CompressionOptimizer.decompress_content</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t187">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t187"><data value='init__'>PrefetchOptimizer.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t210">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t210"><data value='optimize'>PrefetchOptimizer.optimize</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t232">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t232"><data value='record_access'>PrefetchOptimizer.record_access</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t249">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t249"><data value='identify_prefetch_candidates'>PrefetchOptimizer._identify_prefetch_candidates</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t301">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t301"><data value='should_prefetch'>PrefetchOptimizer._should_prefetch</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t315">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t315"><data value='prefetch_content'>PrefetchOptimizer._prefetch_content</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t322">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t322"><data value='cleanup_futures'>PrefetchOptimizer._cleanup_futures</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t336">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t336"><data value='init__'>WarmupOptimizer.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t356">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t356"><data value='optimize'>WarmupOptimizer.optimize</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t391">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t391"><data value='warmup_template'>WarmupOptimizer._warmup_template</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t400">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t400"><data value='warmup_diagram'>WarmupOptimizer._warmup_diagram</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t409">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t409"><data value='add_warmup_content'>WarmupOptimizer.add_warmup_content</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t430">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t430"><data value='init__'>AdaptiveOptimizer.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t440">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t440"><data value='optimize'>AdaptiveOptimizer.optimize</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t479">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t479"><data value='select_optimizations'>AdaptiveOptimizer._select_optimizations</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t497">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t497"><data value='get_optimization_report'>AdaptiveOptimizer.get_optimization_report</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t525">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html#t525"><data value='calculate_average_benefit'>AdaptiveOptimizer._calculate_average_benefit</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html">mermaid_render/cache/optimization.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_optimization_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t30">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t30"><data value='to_dict'>RenderingMetrics.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t56">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t56"><data value='to_dict'>CacheMetrics.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t86">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t86"><data value='to_dict'>PerformanceReport.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t112">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t112"><data value='init__'>PerformanceMonitor.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t148">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t148"><data value='record_render_operation'>PerformanceMonitor.record_render_operation</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t197">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t197"><data value='record_cache_hit'>PerformanceMonitor.record_cache_hit</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t201">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t201"><data value='record_cache_miss'>PerformanceMonitor.record_cache_miss</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t205">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t205"><data value='record_cache_put'>PerformanceMonitor.record_cache_put</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t209">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t209"><data value='record_cache_error'>PerformanceMonitor.record_cache_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t213">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t213"><data value='record_cache_operation'>PerformanceMonitor._record_cache_operation</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t242">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t242"><data value='get_current_stats'>PerformanceMonitor.get_current_stats</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t266">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t266"><data value='get_report'>PerformanceMonitor.get_report</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t359">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t359"><data value='calculate_size_distribution'>PerformanceMonitor._calculate_size_distribution</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t381">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t381"><data value='calculate_format_distribution'>PerformanceMonitor._calculate_format_distribution</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t390">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t390"><data value='calculate_diagram_type_distribution'>PerformanceMonitor._calculate_diagram_type_distribution</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t399">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t399"><data value='calculate_performance_trends'>PerformanceMonitor._calculate_performance_trends</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t432">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html#t432"><data value='generate_recommendations'>PerformanceMonitor._generate_recommendations</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html">mermaid_render/cache/performance.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_performance_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>58</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="58 58">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t25">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t25"><data value='get'>CacheBackend.get</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t30">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t30"><data value='put'>CacheBackend.put</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t35">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t35"><data value='delete'>CacheBackend.delete</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t40">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t40"><data value='clear'>CacheBackend.clear</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t45">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t45"><data value='keys'>CacheBackend.keys</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t50">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t50"><data value='size'>CacheBackend.size</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t54">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t54"><data value='get_all_entries'>CacheBackend.get_all_entries</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t72">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t72"><data value='init__'>MemoryBackend.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t83">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t83"><data value='get'>MemoryBackend.get</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t88">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t88"><data value='put'>MemoryBackend.put</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t99">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t99"><data value='delete'>MemoryBackend.delete</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t107">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t107"><data value='clear'>MemoryBackend.clear</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t112">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t112"><data value='keys'>MemoryBackend.keys</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t117">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t117"><data value='size'>MemoryBackend.size</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t131">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t131"><data value='init__'>FileBackend.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t148">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t148"><data value='init_database'>FileBackend._init_database</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t168">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t168"><data value='get'>FileBackend.get</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t221">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t221"><data value='put'>FileBackend.put</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t268">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t268"><data value='delete'>FileBackend.delete</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t299">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t299"><data value='clear'>FileBackend.clear</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t323">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t323"><data value='keys'>FileBackend.keys</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t335">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t335"><data value='size'>FileBackend.size</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t356">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t356"><data value='init__'>RedisBackend.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t391">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t391"><data value='serialize'>RedisBackend._serialize</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t400">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t400"><data value='deserialize'>RedisBackend._deserialize</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t411">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t411"><data value='make_key'>RedisBackend._make_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t415">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t415"><data value='get'>RedisBackend.get</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t429">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t429"><data value='put'>RedisBackend.put</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t443">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t443"><data value='delete'>RedisBackend.delete</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t452">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t452"><data value='clear'>RedisBackend.clear</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t462">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t462"><data value='keys'>RedisBackend.keys</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t473">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t473"><data value='size'>RedisBackend.size</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t490">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t490"><data value='init__'>CompositeCacheBackend.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t502">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t502"><data value='get'>CompositeCacheBackend.get</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t522">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t522"><data value='put'>CompositeCacheBackend.put</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t536">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t536"><data value='delete'>CompositeCacheBackend.delete</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t550">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t550"><data value='clear'>CompositeCacheBackend.clear</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t559">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t559"><data value='keys'>CompositeCacheBackend.keys</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t569">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html#t569"><data value='size'>CompositeCacheBackend.size</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html">mermaid_render/cache/storage_backends.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_storage_backends_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t27">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t27"><data value='from_cache_entry'>EvictionCandidate.from_cache_entry</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t43">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t43"><data value='select_for_eviction'>CacheStrategy.select_for_eviction</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t63">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t63"><data value='should_cache'>CacheStrategy.should_cache</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t85">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t85"><data value='init__'>TTLStrategy.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t96">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t96"><data value='select_for_eviction'>TTLStrategy.select_for_eviction</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t142">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t142"><data value='should_cache'>TTLStrategy.should_cache</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t156">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t156"><data value='init__'>LRUStrategy.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t167">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t167"><data value='select_for_eviction'>LRUStrategy.select_for_eviction</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t217">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t217"><data value='should_cache'>LRUStrategy.should_cache</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t231">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t231"><data value='init__'>SizeBasedStrategy.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t242">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t242"><data value='select_for_eviction'>SizeBasedStrategy.select_for_eviction</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t299">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t299"><data value='should_cache'>SizeBasedStrategy.should_cache</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t328">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t328"><data value='init__'>SmartStrategy.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t350">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t350"><data value='select_for_eviction'>SmartStrategy.select_for_eviction</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t386">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t386"><data value='should_cache'>SmartStrategy.should_cache</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t431">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t431"><data value='calculate_smart_score'>SmartStrategy._calculate_smart_score</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t465">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t465"><data value='calculate_recent_hit_rate'>SmartStrategy._calculate_recent_hit_rate</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t482">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t482"><data value='record_access'>SmartStrategy.record_access</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t498">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html#t498"><data value='record_performance'>SmartStrategy.record_performance</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html">mermaid_render/cache/strategies.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_strategies_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>9</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t22">mermaid_render/cache/utils.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t22"><data value='create_cache_manager'>create_cache_manager</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t86">mermaid_render/cache/utils.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t86"><data value='create_backend'>_create_backend</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t135">mermaid_render/cache/utils.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t135"><data value='create_strategy'>_create_strategy</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t166">mermaid_render/cache/utils.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t166"><data value='warm_cache'>warm_cache</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t217">mermaid_render/cache/utils.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t217"><data value='clear_cache'>clear_cache</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t272">mermaid_render/cache/utils.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t272"><data value='get_cache_stats'>get_cache_stats</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t348">mermaid_render/cache/utils.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t348"><data value='optimize_cache'>optimize_cache</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t385">mermaid_render/cache/utils.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t385"><data value='export_cache_data'>export_cache_data</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t449">mermaid_render/cache/utils.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html#t449"><data value='import_cache_data'>import_cache_data</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html">mermaid_render/cache/utils.py</a></td>
                <td class="name left"><a href="z_b9d108d10aeee06a_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c___init___py.html">mermaid_render/collaboration/__init__.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="1 8">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t44">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t44"><data value='post_init__'>Activity.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t48">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t48"><data value='to_dict'>Activity.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t69">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t69"><data value='add_activity'>AuditTrail.add_activity</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t72">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t72"><data value='get_activities_by_type'>AuditTrail.get_activities_by_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t75">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t75"><data value='get_activities_by_user'>AuditTrail.get_activities_by_user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t78">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t78"><data value='to_dict'>AuditTrail.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t89">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t89"><data value='init__'>ActivityLogger.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t93">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t93"><data value='log_activity'>ActivityLogger.log_activity</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t128">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t128"><data value='get_session_activities'>ActivityLogger.get_session_activities</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t151">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t151"><data value='get_user_activities'>ActivityLogger.get_user_activities</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t177">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html#t177"><data value='get_activity_summary'>ActivityLogger.get_activity_summary</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html">mermaid_render/collaboration/activity_log.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_activity_log_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t48">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t48"><data value='post_init__'>Collaborator.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t52">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t52"><data value='update_activity'>Collaborator.update_activity</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t56">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t56"><data value='set_online'>Collaborator.set_online</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t62">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t62"><data value='update_cursor'>Collaborator.update_cursor</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t67">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t67"><data value='update_selection'>Collaborator.update_selection</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t72">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t72"><data value='to_dict'>Collaborator.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t103">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t103"><data value='add_collaborator'>CollaborativeSession.add_collaborator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t108">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t108"><data value='remove_collaborator'>CollaborativeSession.remove_collaborator</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t116">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t116"><data value='get_collaborator'>CollaborativeSession.get_collaborator</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t120">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t120"><data value='update_collaborator_permission'>CollaborativeSession.update_collaborator_permission</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t128">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t128"><data value='get_online_collaborators'>CollaborativeSession.get_online_collaborators</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t132">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t132"><data value='can_edit'>CollaborativeSession.can_edit</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t139">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t139"><data value='can_comment'>CollaborativeSession.can_comment</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t148">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t148"><data value='to_dict'>CollaborativeSession.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t173">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t173"><data value='init__'>CollaborationManager.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t185">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t185"><data value='create_session'>CollaborationManager.create_session</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t254">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t254"><data value='get_session'>CollaborationManager.get_session</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t259">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t259"><data value='add_collaborator'>CollaborationManager.add_collaborator</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t315">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t315"><data value='remove_collaborator'>CollaborationManager.remove_collaborator</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t362">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t362"><data value='update_collaborator_status'>CollaborationManager.update_collaborator_status</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t403">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t403"><data value='update_permission'>CollaborationManager.update_permission</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t441">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t441"><data value='get_user_sessions'>CollaborationManager.get_user_sessions</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t447">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t447"><data value='archive_session'>CollaborationManager.archive_session</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t467">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t467"><data value='get_session_statistics'>CollaborationManager.get_session_statistics</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t488">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t488"><data value='on_session_created'>CollaborationManager.on_session_created</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t492">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t492"><data value='on_collaborator_joined'>CollaborationManager.on_collaborator_joined</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t496">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t496"><data value='on_collaborator_left'>CollaborationManager.on_collaborator_left</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t500">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html#t500"><data value='on_permission_changed'>CollaborationManager.on_permission_changed</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html">mermaid_render/collaboration/collaboration_manager.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_collaboration_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>70</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="7 70">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t33">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t33"><data value='post_init__'>Comment.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t37">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t37"><data value='to_dict'>Comment.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t61">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t61"><data value='add_comment'>CommentThread.add_comment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t64">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t64"><data value='resolve_thread'>CommentThread.resolve_thread</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t69">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t69"><data value='to_dict'>CommentThread.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t92">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t92"><data value='post_init__'>Review.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t96">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t96"><data value='add_comment'>Review.add_comment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t99">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t99"><data value='to_dict'>Review.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t116">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t116"><data value='init__'>CommentSystem.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t121">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t121"><data value='add_comment'>CommentSystem.add_comment</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t174">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t174"><data value='update_comment'>CommentSystem.update_comment</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t185">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t185"><data value='resolve_comment'>CommentSystem.resolve_comment</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t201">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t201"><data value='create_review'>CommentSystem.create_review</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t226">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t226"><data value='update_review_status'>CommentSystem.update_review_status</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t237">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t237"><data value='get_comments_for_element'>CommentSystem.get_comments_for_element</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t244">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t244"><data value='get_reviews_for_commit'>CommentSystem.get_reviews_for_commit</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t251">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html#t251"><data value='find_thread_for_comment'>CommentSystem._find_thread_for_comment</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html">mermaid_render/collaboration/comments.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_comments_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t26">mermaid_render/collaboration/diff_engine.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t26"><data value='to_dict'>Change.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t43">mermaid_render/collaboration/diff_engine.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t43"><data value='to_dict'>DiagramDiff.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t58">mermaid_render/collaboration/diff_engine.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t58"><data value='to_dict'>ConflictResolution.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t69">mermaid_render/collaboration/diff_engine.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html#t69"><data value='compute_diff'>DiffEngine.compute_diff</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html">mermaid_render/collaboration/diff_engine.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_diff_engine_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t28">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t28"><data value='to_dict'>MergeConflict.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t41">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t41"><data value='detect_conflicts'>ConflictResolver.detect_conflicts</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t71">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t71"><data value='resolve_conflicts'>ConflictResolver.resolve_conflicts</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t92">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t92"><data value='determine_conflict_type'>ConflictResolver._determine_conflict_type</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t105">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t105"><data value='auto_resolve_conflict'>ConflictResolver._auto_resolve_conflict</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t139">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t139"><data value='init__'>MergeResolver.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t142">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t142"><data value='merge_changes'>MergeResolver.merge_changes</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t165">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t165"><data value='apply_changes'>MergeResolver._apply_changes</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t183">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html#t183"><data value='apply_resolutions'>MergeResolver._apply_resolutions</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html">mermaid_render/collaboration/merge_resolver.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_merge_resolver_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t10">mermaid_render/collaboration/utils.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t10"><data value='create_collaborative_session'>create_collaborative_session</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t35">mermaid_render/collaboration/utils.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t35"><data value='invite_collaborator'>invite_collaborator</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t58">mermaid_render/collaboration/utils.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t58"><data value='commit_diagram_changes'>commit_diagram_changes</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t98">mermaid_render/collaboration/utils.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t98"><data value='create_branch'>create_branch</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t120">mermaid_render/collaboration/utils.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t120"><data value='merge_branches'>merge_branches</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t142">mermaid_render/collaboration/utils.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t142"><data value='resolve_conflicts'>resolve_conflicts</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t162">mermaid_render/collaboration/utils.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t162"><data value='add_comment'>add_comment</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t187">mermaid_render/collaboration/utils.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html#t187"><data value='get_activity_log'>get_activity_log</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html">mermaid_render/collaboration/utils.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t37">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t37"><data value='post_init__'>ChangeSet.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t41">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t41"><data value='to_dict'>ChangeSet.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t69">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t69"><data value='post_init__'>Commit.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t73">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t73"><data value='to_dict'>Commit.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t101">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t101"><data value='to_dict'>Branch.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t124">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t124"><data value='to_dict'>DiagramVersion.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t145">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t145"><data value='to_dict'>MergeResult.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t164">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t164"><data value='init__'>VersionControl.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t180">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t180"><data value='create_initial_branch'>VersionControl._create_initial_branch</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t192">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t192"><data value='commit_changes'>VersionControl.commit_changes</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t260">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t260"><data value='create_branch'>VersionControl.create_branch</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t312">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t312"><data value='merge_branches'>VersionControl.merge_branches</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t393">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t393"><data value='get_commit_history'>VersionControl.get_commit_history</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t424">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t424"><data value='get_version'>VersionControl.get_version</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t431">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t431"><data value='get_diff'>VersionControl.get_diff</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t446">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t446"><data value='calculate_diagram_hash'>VersionControl._calculate_diagram_hash</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t452">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t452"><data value='get_branch_commits'>VersionControl._get_branch_commits</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t468">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t468"><data value='find_common_ancestor'>VersionControl._find_common_ancestor</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t482">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t482"><data value='get_changes_since_commit'>VersionControl._get_changes_since_commit</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t498">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html#t498"><data value='detect_conflicts'>VersionControl._detect_conflicts</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html">mermaid_render/collaboration/version_control.py</a></td>
                <td class="name left"><a href="z_f205c9c10721280c_version_control_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf___init___py.html">mermaid_render/config/__init__.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t66">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t66"><data value='init__'>ConfigManager.__init__</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t98">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t98"><data value='get'>ConfigManager.get</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t116">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t116"><data value='set'>ConfigManager.set</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t134">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t134"><data value='update'>ConfigManager.update</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t149">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t149"><data value='get_all'>ConfigManager.get_all</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t155">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t155"><data value='reset_to_defaults'>ConfigManager.reset_to_defaults</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t162">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t162"><data value='save_to_file'>ConfigManager.save_to_file</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t184">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t184"><data value='validate_config'>ConfigManager.validate_config</data></a></td>
                <td>23</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="16 23">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t227">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t227"><data value='load_defaults'>ConfigManager._load_defaults</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t231">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t231"><data value='load_config_file'>ConfigManager._load_config_file</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t250">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t250"><data value='load_environment'>ConfigManager._load_environment</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t259">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t259"><data value='convert_env_value'>ConfigManager._convert_env_value</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t284">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t284"><data value='process_config'>ConfigManager._process_config</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t289">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html#t289"><data value='process_paths'>ConfigManager._process_paths</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html">mermaid_render/config/config_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_config_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t70">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t70"><data value='init__'>ThemeManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t81">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t81"><data value='get_theme'>ThemeManager.get_theme</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t106">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t106"><data value='get_available_themes'>ThemeManager.get_available_themes</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t112">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t112"><data value='get_built_in_themes'>ThemeManager.get_built_in_themes</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t116">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t116"><data value='get_custom_themes'>ThemeManager.get_custom_themes</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t120">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t120"><data value='is_theme_available'>ThemeManager.is_theme_available</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t127">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t127"><data value='add_custom_theme'>ThemeManager.add_custom_theme</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t157">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t157"><data value='remove_custom_theme'>ThemeManager.remove_custom_theme</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t183">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t183"><data value='create_theme_variant'>ThemeManager.create_theme_variant</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t205">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t205"><data value='validate_theme_config'>ThemeManager._validate_theme_config</data></a></td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t234">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t234"><data value='is_valid_color'>ThemeManager._is_valid_color</data></a></td>
                <td>4</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="2 4">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t256">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t256"><data value='load_custom_themes'>ThemeManager._load_custom_themes</data></a></td>
                <td>11</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="2 11">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t274">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t274"><data value='save_theme_to_file'>ThemeManager._save_theme_to_file</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t285">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t285"><data value='export_theme'>ThemeManager.export_theme</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t298">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html#t298"><data value='import_theme'>ThemeManager.import_theme</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html">mermaid_render/config/theme_manager.py</a></td>
                <td class="name left"><a href="z_b661560c3a90e2cf_theme_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t32">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t32"><data value='init__'>MermaidConfig.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t51">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t51"><data value='get'>MermaidConfig.get</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t55">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t55"><data value='set'>MermaidConfig.set</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t59">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t59"><data value='update'>MermaidConfig.update</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t63">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t63"><data value='to_dict'>MermaidConfig.to_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t83">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t83"><data value='init__'>MermaidTheme.__init__</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t103">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t103"><data value='to_dict'>MermaidTheme.to_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t107">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t107"><data value='apply_to_config'>MermaidTheme.apply_to_config</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t121">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t121"><data value='init__'>MermaidDiagram.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t133">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t133"><data value='get_diagram_type'>MermaidDiagram.get_diagram_type</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t138">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t138"><data value='to_mermaid'>MermaidDiagram.to_mermaid</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t142">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t142"><data value='add_config'>MermaidDiagram.add_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t146">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t146"><data value='get_config'>MermaidDiagram.get_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t150">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t150"><data value='validate'>MermaidDiagram.validate</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t163">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t163"><data value='str__'>MermaidDiagram.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t177">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t177"><data value='init__'>MermaidRenderer.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t195">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t195"><data value='set_theme'>MermaidRenderer.set_theme</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t209">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t209"><data value='get_theme'>MermaidRenderer.get_theme</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t213">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t213"><data value='render'>MermaidRenderer.render</data></a></td>
                <td>15</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="14 15">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t259">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t259"><data value='render_raw'>MermaidRenderer.render_raw</data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t293">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html#t293"><data value='save'>MermaidRenderer.save</data></a></td>
                <td>14</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="10 14">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html">mermaid_render/core.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_core_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t19">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t19"><data value='init__'>MermaidRenderError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t31">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t31"><data value='str__'>MermaidRenderError.__str__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t48">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t48"><data value='init__'>ValidationError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t66">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t66"><data value='str__'>ValidationError.__str__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t89">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t89"><data value='init__'>RenderingError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t107">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t107"><data value='str__'>RenderingError.__str__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t130">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t130"><data value='init__'>ConfigurationError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t148">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t148"><data value='str__'>ConfigurationError.__str__</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t170">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t170"><data value='init__'>UnsupportedFormatError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t188">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t188"><data value='str__'>UnsupportedFormatError.__str__</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t211">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t211"><data value='init__'>NetworkError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t229">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t229"><data value='str__'>NetworkError.__str__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t252">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t252"><data value='init__'>ThemeError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t270">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t270"><data value='str__'>ThemeError.__str__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t293">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t293"><data value='init__'>DiagramError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t311">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t311"><data value='str__'>DiagramError.__str__</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t335">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t335"><data value='init__'>TemplateError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t353">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t353"><data value='str__'>TemplateError.__str__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t377">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t377"><data value='init__'>DataSourceError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t395">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t395"><data value='str__'>DataSourceError.__str__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t419">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t419"><data value='init__'>CacheError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t437">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html#t437"><data value='str__'>CacheError.__str__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html">mermaid_render/exceptions.py</a></td>
                <td class="name left"><a href="z_f14f9617df6ea276_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1___init___py.html">mermaid_render/interactive/__init__.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="2 9">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t43">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t43"><data value='to_dict'>Position.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t47">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t47"><data value='from_dict'>Position.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t57">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t57"><data value='to_dict'>Size.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t61">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t61"><data value='from_dict'>Size.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t85">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t85"><data value='post_init__'>DiagramElement.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t89">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t89"><data value='update_position'>DiagramElement.update_position</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t95">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t95"><data value='update_size'>DiagramElement.update_size</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t101">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t101"><data value='update_properties'>DiagramElement.update_properties</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t106">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t106"><data value='update_style'>DiagramElement.update_style</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t111">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t111"><data value='to_dict'>DiagramElement.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t127">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t127"><data value='from_dict'>DiagramElement.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t163">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t163"><data value='post_init__'>DiagramConnection.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t167">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t167"><data value='update_label'>DiagramConnection.update_label</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t172">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t172"><data value='update_style'>DiagramConnection.update_style</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t177">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t177"><data value='add_control_point'>DiagramConnection.add_control_point</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t182">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t182"><data value='to_dict'>DiagramConnection.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t198">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t198"><data value='from_dict'>DiagramConnection.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t222">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t222"><data value='init__'>DiagramBuilder.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t247">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t247"><data value='add_element'>DiagramBuilder.add_element</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t292">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t292"><data value='update_element'>DiagramBuilder.update_element</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t340">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t340"><data value='remove_element'>DiagramBuilder.remove_element</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t374">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t374"><data value='add_connection'>DiagramBuilder.add_connection</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t419">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t419"><data value='update_connection'>DiagramBuilder.update_connection</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t463">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t463"><data value='remove_connection'>DiagramBuilder.remove_connection</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t486">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t486"><data value='generate_mermaid_code'>DiagramBuilder.generate_mermaid_code</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t502">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t502"><data value='load_from_mermaid_code'>DiagramBuilder.load_from_mermaid_code</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t513">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t513"><data value='to_dict'>DiagramBuilder.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t522">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t522"><data value='from_dict'>DiagramBuilder.from_dict</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t535">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t535"><data value='get_default_size'>DiagramBuilder._get_default_size</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t544">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t544"><data value='update_metadata'>DiagramBuilder._update_metadata</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t550">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t550"><data value='generate_flowchart_code'>DiagramBuilder._generate_flowchart_code</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t578">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t578"><data value='get_flowchart_node_syntax'>DiagramBuilder._get_flowchart_node_syntax</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t589">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t589"><data value='get_flowchart_arrow'>DiagramBuilder._get_flowchart_arrow</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t599">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t599"><data value='generate_sequence_code'>DiagramBuilder._generate_sequence_code</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t604">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t604"><data value='generate_class_code'>DiagramBuilder._generate_class_code</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t610">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t610"><data value='on_element_added'>DiagramBuilder.on_element_added</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t614">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t614"><data value='on_element_updated'>DiagramBuilder.on_element_updated</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t618">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t618"><data value='on_element_removed'>DiagramBuilder.on_element_removed</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t622">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t622"><data value='on_connection_added'>DiagramBuilder.on_connection_added</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t626">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t626"><data value='on_connection_updated'>DiagramBuilder.on_connection_updated</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t630">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html#t630"><data value='on_connection_removed'>DiagramBuilder.on_connection_removed</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html">mermaid_render/interactive/builder.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_builder_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>98</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="98 98">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t19">mermaid_render/interactive/export.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t19"><data value='init__'>ExportManager.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t22">mermaid_render/interactive/export.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t22"><data value='export_diagram'>ExportManager.export_diagram</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t32">mermaid_render/interactive/export.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t32"><data value='export_mermaid_code'>ExportManager._export_mermaid_code</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t37">mermaid_render/interactive/export.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html#t37"><data value='export_json'>ExportManager._export_json</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html">mermaid_render/interactive/export.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_export_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t36">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t36"><data value='init__'>InteractiveServer.__init__</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t80">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t80"><data value='create_app'>InteractiveServer._create_app</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t97">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t97"><data value='index'>InteractiveServer._create_app.index</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t102">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t102"><data value='builder'>InteractiveServer._create_app.builder</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t115">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t115"><data value='create_session'>InteractiveServer._create_app.create_session</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t133">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t133"><data value='get_session'>InteractiveServer._create_app.get_session</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t148">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t148"><data value='add_element'>InteractiveServer._create_app.add_element</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t181">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t181"><data value='update_element'>InteractiveServer._create_app.update_element</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t220">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t220"><data value='remove_element'>InteractiveServer._create_app.remove_element</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t244">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t244"><data value='add_connection'>InteractiveServer._create_app.add_connection</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t276">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t276"><data value='get_mermaid_code'>InteractiveServer._create_app.get_mermaid_code</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t306">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t306"><data value='get_preview'>InteractiveServer._create_app.get_preview</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t327">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t327"><data value='websocket_endpoint'>InteractiveServer._create_app.websocket_endpoint</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t348">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t348"><data value='run'>InteractiveServer.run</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t358">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t358"><data value='create_app'>create_app</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t382">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html#t382"><data value='start_server'>start_server</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html">mermaid_render/interactive/server.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_server_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="7 22">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t23">mermaid_render/interactive/templates.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t23"><data value='init__'>TemplateLibrary.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t26">mermaid_render/interactive/templates.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t26"><data value='get_template'>TemplateLibrary.get_template</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t29">mermaid_render/interactive/templates.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t29"><data value='list_templates'>TemplateLibrary.list_templates</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t32">mermaid_render/interactive/templates.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html#t32"><data value='load_default_templates'>TemplateLibrary._load_default_templates</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html">mermaid_render/interactive/templates.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_templates_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t22">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t22"><data value='render'>UIComponent.render</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t31">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t31"><data value='init__'>NodeComponent.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t38">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t38"><data value='render'>NodeComponent.render</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t51">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t51"><data value='init__'>EdgeComponent.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t58">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t58"><data value='render'>EdgeComponent.render</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t70">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t70"><data value='init__'>ToolboxComponent.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t78">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t78"><data value='render'>ToolboxComponent.render</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t86">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t86"><data value='get_default_tools'>ToolboxComponent._get_default_tools</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t99">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t99"><data value='init__'>PropertiesPanel.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t107">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t107"><data value='render'>PropertiesPanel.render</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t115">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t115"><data value='set_selected_element'>PropertiesPanel.set_selected_element</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t122">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t122"><data value='init__'>CodeEditor.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t130">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t130"><data value='render'>CodeEditor.render</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t139">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t139"><data value='set_code'>CodeEditor.set_code</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t146">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t146"><data value='init__'>PreviewPanel.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t154">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t154"><data value='render'>PreviewPanel.render</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t163">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html#t163"><data value='set_preview_content'>PreviewPanel.set_preview_content</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html">mermaid_render/interactive/ui_components.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_ui_components_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>2</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html#t8">mermaid_render/interactive/utils.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html#t8"><data value='create_interactive_session'>create_interactive_session</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html#t13">mermaid_render/interactive/utils.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html#t13"><data value='load_diagram_from_code'>load_diagram_from_code</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html#t20">mermaid_render/interactive/utils.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html#t20"><data value='export_diagram_code'>export_diagram_code</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html#t25">mermaid_render/interactive/utils.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html#t25"><data value='validate_diagram_live'>validate_diagram_live</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html#t32">mermaid_render/interactive/utils.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html#t32"><data value='get_available_components'>get_available_components</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html">mermaid_render/interactive/utils.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t26">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t26"><data value='to_dict'>ValidationIssue.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t47">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t47"><data value='to_dict'>ValidationResult.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t65">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t65"><data value='init__'>LiveValidator.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t78">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t78"><data value='validate'>LiveValidator.validate</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t133">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t133"><data value='validate_element'>LiveValidator.validate_element</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t193">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t193"><data value='validate_connection'>LiveValidator.validate_connection</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t252">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t252"><data value='validate_interactive_rules'>LiveValidator._validate_interactive_rules</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t305">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t305"><data value='get_performance_suggestions'>LiveValidator._get_performance_suggestions</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t341">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t341"><data value='get_accessibility_suggestions'>LiveValidator._get_accessibility_suggestions</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t377">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t377"><data value='get_error_suggestion'>LiveValidator._get_error_suggestion</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t392">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html#t392"><data value='get_warning_suggestion'>LiveValidator._get_warning_suggestion</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html">mermaid_render/interactive/validation.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_validation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t28">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t28"><data value='init__'>DiagramSession.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t35">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t35"><data value='add_client'>DiagramSession.add_client</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t40">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t40"><data value='remove_client'>DiagramSession.remove_client</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t45">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t45"><data value='get_client_count'>DiagramSession.get_client_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t58">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t58"><data value='init__'>WebSocketHandler.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t63">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t63"><data value='connect'>WebSocketHandler.connect</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t90">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t90"><data value='disconnect'>WebSocketHandler.disconnect</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t109">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t109"><data value='handle_message'>WebSocketHandler.handle_message</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t144">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t144"><data value='broadcast_to_session'>WebSocketHandler.broadcast_to_session</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t156">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t156"><data value='send_current_state'>WebSocketHandler._send_current_state</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t170">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t170"><data value='broadcast_client_update'>WebSocketHandler._broadcast_client_update</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t180">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t180"><data value='send_to_session_clients'>WebSocketHandler._send_to_session_clients</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t201">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t201"><data value='handle_element_update'>WebSocketHandler._handle_element_update</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t234">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t234"><data value='handle_connection_update'>WebSocketHandler._handle_connection_update</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t263">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t263"><data value='handle_cursor_update'>WebSocketHandler._handle_cursor_update</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t273">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t273"><data value='handle_selection_update'>WebSocketHandler._handle_selection_update</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t283">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t283"><data value='handle_chat_message'>WebSocketHandler._handle_chat_message</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t294">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t294"><data value='get_session_info'>WebSocketHandler.get_session_info</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t310">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html#t310"><data value='get_all_sessions'>WebSocketHandler.get_all_sessions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html">mermaid_render/interactive/websocket_handler.py</a></td>
                <td class="name left"><a href="z_f973c03743310cc1_websocket_handler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602___init___py.html">mermaid_render/models/__init__.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t16">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t16"><data value='init__'>ClassMethod.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t33">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t33"><data value='to_mermaid'>ClassMethod.to_mermaid</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t55">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t55"><data value='init__'>ClassAttribute.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t68">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t68"><data value='to_mermaid'>ClassAttribute.to_mermaid</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t85">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t85"><data value='init__'>ClassDefinition.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t100">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t100"><data value='add_attribute'>ClassDefinition.add_attribute</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t104">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t104"><data value='add_method'>ClassDefinition.add_method</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t108">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t108"><data value='to_mermaid'>ClassDefinition.to_mermaid</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t150">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t150"><data value='init__'>ClassRelationship.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t170">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t170"><data value='to_mermaid'>ClassRelationship.to_mermaid</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t206">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t206"><data value='init__'>ClassDiagram.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t212">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t212"><data value='get_diagram_type'>ClassDiagram.get_diagram_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t216">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t216"><data value='add_class'>ClassDiagram.add_class</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t231">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t231"><data value='add_relationship'>ClassDiagram.add_relationship</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t252">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html#t252"><data value='to_mermaid'>ClassDiagram.to_mermaid</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html">mermaid_render/models/class_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_class_diagram_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t11">mermaid_render/models/er_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t11"><data value='init__'>ERDiagram.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t16">mermaid_render/models/er_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t16"><data value='get_diagram_type'>ERDiagram.get_diagram_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t19">mermaid_render/models/er_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t19"><data value='add_entity'>ERDiagram.add_entity</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t23">mermaid_render/models/er_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t23"><data value='add_relationship'>ERDiagram.add_relationship</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t27">mermaid_render/models/er_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html#t27"><data value='to_mermaid'>ERDiagram.to_mermaid</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html">mermaid_render/models/er_diagram.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_er_diagram_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t32">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t32"><data value='init__'>FlowchartNode.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t56">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t56"><data value='to_mermaid'>FlowchartNode.to_mermaid</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t74">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t74"><data value='init__'>FlowchartEdge.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t101">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t101"><data value='to_mermaid'>FlowchartEdge.to_mermaid</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t115">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t115"><data value='init__'>FlowchartSubgraph.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t134">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t134"><data value='add_node'>FlowchartSubgraph.add_node</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t139">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t139"><data value='to_mermaid'>FlowchartSubgraph.to_mermaid</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t177">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t177"><data value='init__'>FlowchartDiagram.__init__</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t200">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t200"><data value='get_diagram_type'>FlowchartDiagram.get_diagram_type</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t204">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t204"><data value='add_node'>FlowchartDiagram.add_node</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t230">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t230"><data value='add_edge'>FlowchartDiagram.add_edge</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t260">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t260"><data value='add_subgraph'>FlowchartDiagram.add_subgraph</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t284">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t284"><data value='add_node_to_subgraph'>FlowchartDiagram.add_node_to_subgraph</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t293">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t293"><data value='add_style'>FlowchartDiagram.add_style</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t297">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html#t297"><data value='to_mermaid'>FlowchartDiagram.to_mermaid</data></a></td>
                <td>16</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="11 16">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html">mermaid_render/models/flowchart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_flowchart_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t11">mermaid_render/models/gantt.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t11"><data value='init__'>GanttDiagram.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t17">mermaid_render/models/gantt.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t17"><data value='get_diagram_type'>GanttDiagram.get_diagram_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t20">mermaid_render/models/gantt.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t20"><data value='add_section'>GanttDiagram.add_section</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t24">mermaid_render/models/gantt.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t24"><data value='add_task'>GanttDiagram.add_task</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t34">mermaid_render/models/gantt.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html#t34"><data value='to_mermaid'>GanttDiagram.to_mermaid</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html">mermaid_render/models/gantt.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_gantt_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t10">mermaid_render/models/git_graph.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t10"><data value='init__'>GitGraphDiagram.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t16">mermaid_render/models/git_graph.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t16"><data value='get_diagram_type'>GitGraphDiagram.get_diagram_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t19">mermaid_render/models/git_graph.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t19"><data value='add_commit'>GitGraphDiagram.add_commit</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t23">mermaid_render/models/git_graph.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t23"><data value='add_branch'>GitGraphDiagram.add_branch</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t27">mermaid_render/models/git_graph.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t27"><data value='add_merge'>GitGraphDiagram.add_merge</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t31">mermaid_render/models/git_graph.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html#t31"><data value='to_mermaid'>GitGraphDiagram.to_mermaid</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html">mermaid_render/models/git_graph.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_git_graph_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t10">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t10"><data value='init__'>MindmapNode.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t16">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t16"><data value='add_child'>MindmapNode.add_child</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t20">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t20"><data value='to_mermaid'>MindmapNode.to_mermaid</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t45">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t45"><data value='init__'>MindmapDiagram.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t49">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t49"><data value='get_diagram_type'>MindmapDiagram.get_diagram_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t52">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t52"><data value='add_node'>MindmapDiagram.add_node</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t66">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t66"><data value='find_node'>MindmapDiagram._find_node</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t78">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html#t78"><data value='to_mermaid'>MindmapDiagram.to_mermaid</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html">mermaid_render/models/mindmap.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_mindmap_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html#t10">mermaid_render/models/pie_chart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html#t10"><data value='init__'>PieChartDiagram.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html#t15">mermaid_render/models/pie_chart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html#t15"><data value='get_diagram_type'>PieChartDiagram.get_diagram_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html#t18">mermaid_render/models/pie_chart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html#t18"><data value='add_slice'>PieChartDiagram.add_slice</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html#t22">mermaid_render/models/pie_chart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html#t22"><data value='to_mermaid'>PieChartDiagram.to_mermaid</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html">mermaid_render/models/pie_chart.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_pie_chart_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t16">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t16"><data value='init__'>SequenceParticipant.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t27">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t27"><data value='to_mermaid'>SequenceParticipant.to_mermaid</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t48">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t48"><data value='init__'>SequenceMessage.__init__</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t78">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t78"><data value='to_mermaid'>SequenceMessage.to_mermaid</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t102">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t102"><data value='init__'>SequenceNote.__init__</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t126">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t126"><data value='to_mermaid'>SequenceNote.to_mermaid</data></a></td>
                <td>4</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="2 4">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t138">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t138"><data value='init__'>SequenceLoop.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t149">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t149"><data value='add_message'>SequenceLoop.add_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t153">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t153"><data value='add_note'>SequenceLoop.add_note</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t157">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t157"><data value='to_mermaid'>SequenceLoop.to_mermaid</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t186">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t186"><data value='init__'>SequenceDiagram.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t202">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t202"><data value='get_diagram_type'>SequenceDiagram.get_diagram_type</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t206">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t206"><data value='add_participant'>SequenceDiagram.add_participant</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t224">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t224"><data value='add_message'>SequenceDiagram.add_message</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t259">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t259"><data value='add_note'>SequenceDiagram.add_note</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t285">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t285"><data value='add_loop'>SequenceDiagram.add_loop</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t299">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t299"><data value='activate_participant'>SequenceDiagram.activate_participant</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t305">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t305"><data value='deactivate_participant'>SequenceDiagram.deactivate_participant</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t311">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html#t311"><data value='to_mermaid'>SequenceDiagram.to_mermaid</data></a></td>
                <td>16</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="14 16">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html">mermaid_render/models/sequence.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_sequence_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t11">mermaid_render/models/state.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t11"><data value='init__'>StateDiagram.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t16">mermaid_render/models/state.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t16"><data value='get_diagram_type'>StateDiagram.get_diagram_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t19">mermaid_render/models/state.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t19"><data value='add_state'>StateDiagram.add_state</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t23">mermaid_render/models/state.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t23"><data value='add_transition'>StateDiagram.add_transition</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t27">mermaid_render/models/state.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_state_py.html#t27"><data value='to_mermaid'>StateDiagram.to_mermaid</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_state_py.html">mermaid_render/models/state.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_state_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t10">mermaid_render/models/user_journey.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t10"><data value='init__'>UserJourneyDiagram.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t15">mermaid_render/models/user_journey.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t15"><data value='get_diagram_type'>UserJourneyDiagram.get_diagram_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t18">mermaid_render/models/user_journey.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t18"><data value='add_section'>UserJourneyDiagram.add_section</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t22">mermaid_render/models/user_journey.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t22"><data value='add_task'>UserJourneyDiagram.add_task</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t26">mermaid_render/models/user_journey.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html#t26"><data value='to_mermaid'>UserJourneyDiagram.to_mermaid</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html">mermaid_render/models/user_journey.py</a></td>
                <td class="name left"><a href="z_6130f7308039d602_user_journey_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0___init___py.html">mermaid_render/renderers/__init__.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t20">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t20"><data value='init__'>PDFRenderer.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t38">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t38"><data value='render'>PDFRenderer.render</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t69">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t69"><data value='svg_to_pdf'>PDFRenderer._svg_to_pdf</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t141">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t141"><data value='render_to_file'>PDFRenderer.render_to_file</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t162">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t162"><data value='get_supported_themes'>PDFRenderer.get_supported_themes</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t166">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t166"><data value='validate_theme'>PDFRenderer.validate_theme</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t170">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t170"><data value='get_supported_page_sizes'>PDFRenderer.get_supported_page_sizes</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t174">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html#t174"><data value='set_page_options'>PDFRenderer.set_page_options</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html">mermaid_render/renderers/pdf_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_pdf_renderer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t22">mermaid_render/renderers/png_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t22"><data value='init__'>PNGRenderer.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t43">mermaid_render/renderers/png_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t43"><data value='render'>PNGRenderer.render</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t120">mermaid_render/renderers/png_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t120"><data value='render_to_file'>PNGRenderer.render_to_file</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t145">mermaid_render/renderers/png_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t145"><data value='get_supported_themes'>PNGRenderer.get_supported_themes</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t149">mermaid_render/renderers/png_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t149"><data value='validate_theme'>PNGRenderer.validate_theme</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t153">mermaid_render/renderers/png_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t153"><data value='get_max_dimensions'>PNGRenderer.get_max_dimensions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t158">mermaid_render/renderers/png_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html#t158"><data value='validate_dimensions'>PNGRenderer.validate_dimensions</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html">mermaid_render/renderers/png_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_png_renderer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t23">mermaid_render/renderers/svg_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t23"><data value='init__'>SVGRenderer.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t41">mermaid_render/renderers/svg_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t41"><data value='render'>SVGRenderer.render</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t71">mermaid_render/renderers/svg_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t71"><data value='render_local'>SVGRenderer._render_local</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t116">mermaid_render/renderers/svg_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t116"><data value='render_remote'>SVGRenderer._render_remote</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t176">mermaid_render/renderers/svg_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t176"><data value='render_to_file'>SVGRenderer.render_to_file</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t197">mermaid_render/renderers/svg_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t197"><data value='get_supported_themes'>SVGRenderer.get_supported_themes</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t201">mermaid_render/renderers/svg_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html#t201"><data value='validate_theme'>SVGRenderer.validate_theme</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html">mermaid_render/renderers/svg_renderer.py</a></td>
                <td class="name left"><a href="z_e41e05de37345ab0_svg_renderer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b___init___py.html">mermaid_render/templates/__init__.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t24">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t24"><data value='load_data'>DataSource.load_data</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t29">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t29"><data value='validate_source'>DataSource.validate_source</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t42">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t42"><data value='load_data'>JSONDataSource.load_data</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t80">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t80"><data value='validate_source'>JSONDataSource.validate_source</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t94">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t94"><data value='apply_mapping'>JSONDataSource._apply_mapping</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t106">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t106"><data value='apply_filters'>JSONDataSource._apply_filters</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t123">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t123"><data value='get_nested_value'>JSONDataSource._get_nested_value</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t136">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t136"><data value='matches_filter'>JSONDataSource._matches_filter</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t159">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t159"><data value='load_data'>CSVDataSource.load_data</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t209">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t209"><data value='validate_source'>CSVDataSource.validate_source</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t223">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t223"><data value='apply_row_mapping'>CSVDataSource._apply_row_mapping</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t233">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t233"><data value='rows_to_columns'>CSVDataSource._rows_to_columns</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t244">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t244"><data value='group_rows'>CSVDataSource._group_rows</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t265">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t265"><data value='init__'>DatabaseDataSource.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t274">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t274"><data value='load_data'>DatabaseDataSource.load_data</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t328">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t328"><data value='validate_source'>DatabaseDataSource.validate_source</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t348">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t348"><data value='apply_transform'>DatabaseDataSource._apply_transform</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t378">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t378"><data value='init__'>APIDataSource.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t389">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t389"><data value='load_data'>APIDataSource.load_data</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t454">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t454"><data value='validate_source'>APIDataSource.validate_source</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t472">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t472"><data value='extract_data'>APIDataSource._extract_data</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t491">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t491"><data value='apply_api_transform'>APIDataSource._apply_api_transform</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t505">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t505"><data value='create_data_source'>create_data_source</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t544">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html#t544"><data value='load_template_data'>load_template_data</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html">mermaid_render/templates/data_sources.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_data_sources_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t20">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t20"><data value='generate'>DiagramGenerator.generate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t25">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t25"><data value='get_schema'>DiagramGenerator.get_schema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t38">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t38"><data value='generate'>FlowchartGenerator.generate</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t132">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t132"><data value='get_schema'>FlowchartGenerator.get_schema</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t207">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t207"><data value='generate'>SequenceGenerator.generate</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t271">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t271"><data value='get_schema'>SequenceGenerator.get_schema</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t324">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t324"><data value='generate'>ClassDiagramGenerator.generate</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t411">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t411"><data value='get_schema'>ClassDiagramGenerator.get_schema</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t485">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t485"><data value='generate'>ArchitectureGenerator.generate</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t532">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t532"><data value='get_schema'>ArchitectureGenerator.get_schema</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t577">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t577"><data value='generate'>ProcessFlowGenerator.generate</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t627">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html#t627"><data value='get_schema'>ProcessFlowGenerator.get_schema</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html">mermaid_render/templates/generators.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_generators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t21">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t21"><data value='init__'>BuiltInTemplates.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t24">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t24"><data value='get_all_templates'>BuiltInTemplates.get_all_templates</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t28">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t28"><data value='get_template'>BuiltInTemplates.get_template</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t32">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t32"><data value='list_template_names'>BuiltInTemplates.list_template_names</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t36">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t36"><data value='load_builtin_templates'>BuiltInTemplates._load_builtin_templates</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t337">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t337"><data value='init__'>CommunityTemplates.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t350">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t350"><data value='get_all_templates'>CommunityTemplates.get_all_templates</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t354">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t354"><data value='get_template'>CommunityTemplates.get_template</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t358">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t358"><data value='list_template_names'>CommunityTemplates.list_template_names</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t362">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t362"><data value='submit_template'>CommunityTemplates.submit_template</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t380">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html#t380"><data value='load_online_templates'>CommunityTemplates._load_online_templates</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html">mermaid_render/templates/library.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_library_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t44">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t44"><data value='validate_value'>ParameterSchema.validate_value</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t64">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t64"><data value='validate_type'>ParameterSchema._validate_type</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t83">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t83"><data value='validate_rules'>ParameterSchema._validate_rules</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t134">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t134"><data value='validate_item_type'>ParameterSchema._validate_item_type</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t169">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t169"><data value='validate'>TemplateSchema.validate</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t218">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t218"><data value='validate_jinja_syntax'>TemplateSchema._validate_jinja_syntax</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t229">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t229"><data value='validate_template'>validate_template</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t266">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t266"><data value='validate_template_parameters'>validate_template_parameters</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t307">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t307"><data value='validate_parameter_type'>_validate_parameter_type</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t326">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html#t326"><data value='validate_parameter_rules'>_validate_parameter_rules</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html">mermaid_render/templates/schema.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_schema_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t44">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t44"><data value='post_init__'>Template.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t48">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t48"><data value='to_dict'>Template.to_dict</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t56">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t56"><data value='from_dict'>Template.from_dict</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t63">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t63"><data value='validate'>Template.validate</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t90">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t90"><data value='init__'>TemplateManager.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t124">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t124"><data value='create_template'>TemplateManager.create_template</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t180">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t180"><data value='get_template'>TemplateManager.get_template</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t184">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t184"><data value='get_template_by_name'>TemplateManager.get_template_by_name</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t191">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t191"><data value='list_templates'>TemplateManager.list_templates</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t221">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t221"><data value='generate'>TemplateManager.generate</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t263">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t263"><data value='delete_template'>TemplateManager.delete_template</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t277">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t277"><data value='export_template'>TemplateManager.export_template</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t286">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t286"><data value='import_template'>TemplateManager.import_template</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t302">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t302"><data value='validate_parameters'>TemplateManager._validate_parameters</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t312">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t312"><data value='save_template'>TemplateManager._save_template</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t318">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t318"><data value='load_custom_templates'>TemplateManager._load_custom_templates</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t332">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t332"><data value='load_builtin_templates'>TemplateManager._load_builtin_templates</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t341">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html#t341"><data value='load_community_templates'>TemplateManager._load_community_templates</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html">mermaid_render/templates/template_manager.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_template_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>45</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t17">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t17"><data value='generate_from_template'>generate_from_template</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t52">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t52"><data value='list_available_templates'>list_available_templates</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t94">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t94"><data value='get_template_info'>get_template_info</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t138">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t138"><data value='validate_template_parameters'>validate_template_parameters</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t176">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t176"><data value='export_template'>export_template</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t205">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t205"><data value='import_template'>import_template</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t234">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t234"><data value='create_template_from_diagram'>create_template_from_diagram</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t287">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t287"><data value='get_template_examples'>get_template_examples</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t337">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t337"><data value='generate_basic_example'>_generate_basic_example</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t366">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t366"><data value='search_templates'>search_templates</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t428">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html#t428"><data value='calculate_relevance'>_calculate_relevance</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html">mermaid_render/templates/utils.py</a></td>
                <td class="name left"><a href="z_ce606bda95b0387b_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258___init___py.html">mermaid_render/utils/__init__.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html#t13">mermaid_render/utils/export.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html#t13"><data value='export_to_file'>export_to_file</data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html#t77">mermaid_render/utils/export.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html#t77"><data value='export_multiple_formats'>export_multiple_formats</data></a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html#t129">mermaid_render/utils/export.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html#t129"><data value='batch_export'>batch_export</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html#t185">mermaid_render/utils/export.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html#t185"><data value='detect_format_from_extension'>_detect_format_from_extension</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html#t223">mermaid_render/utils/export.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html#t223"><data value='sanitize_filename'>_sanitize_filename</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html">mermaid_render/utils/export.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_export_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t12">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t12"><data value='get_supported_formats'>get_supported_formats</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t26">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t26"><data value='get_available_themes'>get_available_themes</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t43">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t43"><data value='detect_diagram_type'>detect_diagram_type</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t86">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t86"><data value='sanitize_filename'>sanitize_filename</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t120">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t120"><data value='ensure_directory'>ensure_directory</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t139">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t139"><data value='format_file_size'>format_file_size</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t167">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t167"><data value='get_file_info'>get_file_info</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t199">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t199"><data value='validate_format'>validate_format</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t216">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t216"><data value='validate_theme'>validate_theme</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t233">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t233"><data value='get_diagram_stats'>get_diagram_stats</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t260">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html#t260"><data value='estimate_complexity'>_estimate_complexity</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html">mermaid_render/utils/helpers.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_helpers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t11">mermaid_render/utils/validation.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t11"><data value='validate_mermaid_syntax'>validate_mermaid_syntax</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t39">mermaid_render/utils/validation.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t39"><data value='quick_validate'>quick_validate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t57">mermaid_render/utils/validation.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t57"><data value='get_validation_errors'>get_validation_errors</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t76">mermaid_render/utils/validation.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t76"><data value='get_validation_warnings'>get_validation_warnings</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t97">mermaid_render/utils/validation.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t97"><data value='suggest_fixes'>suggest_fixes</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t116">mermaid_render/utils/validation.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html#t116"><data value='validate_node_id'>validate_node_id</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html">mermaid_render/utils/validation.py</a></td>
                <td class="name left"><a href="z_bc553f56d7d9a258_validation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93___init___py.html">mermaid_render/validators/__init__.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t22">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t22"><data value='bool__'>ValidationResult.__bool__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t26">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t26"><data value='str__'>ValidationResult.__str__</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t70">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t70"><data value='init__'>MermaidValidator.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t77">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t77"><data value='validate'>MermaidValidator.validate</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t111">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t111"><data value='reset'>MermaidValidator._reset</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t118">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t118"><data value='create_result'>MermaidValidator._create_result</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t127">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t127"><data value='add_error'>MermaidValidator._add_error</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t135">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t135"><data value='add_warning'>MermaidValidator._add_warning</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t139">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t139"><data value='detect_diagram_type'>MermaidValidator._detect_diagram_type</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t149">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t149"><data value='validate_structure'>MermaidValidator._validate_structure</data></a></td>
                <td>8</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="4 8">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t165">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t165"><data value='validate_indentation'>MermaidValidator._validate_indentation</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t180">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t180"><data value='validate_syntax'>MermaidValidator._validate_syntax</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t190">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t190"><data value='validate_line_syntax'>MermaidValidator._validate_line_syntax</data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t211">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t211"><data value='validate_diagram_type'>MermaidValidator._validate_diagram_type</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t221">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t221"><data value='validate_flowchart'>MermaidValidator._validate_flowchart</data></a></td>
                <td>21</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="19 21">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t257">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t257"><data value='validate_sequence_diagram'>MermaidValidator._validate_sequence_diagram</data></a></td>
                <td>23</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="21 23">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t291">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t291"><data value='validate_class_diagram'>MermaidValidator._validate_class_diagram</data></a></td>
                <td>19</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="17 19">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t320">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t320"><data value='validate_node_id'>MermaidValidator.validate_node_id</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t324">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html#t324"><data value='suggest_fixes'>MermaidValidator.suggest_fixes</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html">mermaid_render/validators/validator.py</a></td>
                <td class="name left"><a href="z_9bf3ad49003d1e93_validator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>6438</td>
                <td>4934</td>
                <td>104</td>
                <td class="right" data-ratio="1504 6438">23%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 10:26 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
